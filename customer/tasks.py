# # Version 1.0 - Customer-centric page (v01)
# from __future__ import absolute_import, unicode_literals
# from celery import shared_task
# from channels.layers import get_channel_layer
# from asgiref.sync import async_to_sync
# import logging
# from typing import Dict, Any, Optional

# from customer.models import Customer, CustomerPlatformIdentity
# from customer.serializers import CustomerSerializer, CustomerPlatformIdentitySerializer

# logger = logging.getLogger('django.connector')

# @shared_task
# def broadcast_to_customer_websocket(
#     customer_id: int,
#     event_type: str,
#     data: Dict[str, Any]
# ):
#     """
#     Broadcast an event to all WebSocket connections watching a specific customer.
#     """
#     try:
#         channel_layer = get_channel_layer()
#         room_name = f'customer_{customer_id}'
        
#         # Prepare the event
#         event = {
#             'type': event_type,
#             **data
#         }
        
#         # Send to channel layer
#         async_to_sync(channel_layer.group_send)(room_name, event)
        
#         logger.info(f"Broadcasted {event_type} to customer {customer_id}")
        
#         return {
#             'success': True,
#             'customer_id': customer_id,
#             'event_type': event_type
#         }
        
#     except Exception as e:
#         logger.error(f"Error broadcasting to customer WebSocket: {str(e)}")
#         return {
#             'success': False,
#             'error': str(e)
#         }

# @shared_task
# def broadcast_to_platform_websocket(
#     platform_identity_id: int,
#     event_type: str,
#     data: Dict[str, Any]
# ):
#     """
#     Broadcast an event to all WebSocket connections watching a specific platform.
#     """
#     try:
#         channel_layer = get_channel_layer()
#         room_name = f'platform_{platform_identity_id}'
        
#         # Prepare the event
#         event = {
#             'type': event_type,
#             **data
#         }
        
#         # Send to channel layer
#         async_to_sync(channel_layer.group_send)(room_name, event)
        
#         logger.info(f"Broadcasted {event_type} to platform {platform_identity_id}")
        
#         return {
#             'success': True,
#             'platform_identity_id': platform_identity_id,
#             'event_type': event_type
#         }
        
#     except Exception as e:
#         logger.error(f"Error broadcasting to platform WebSocket: {str(e)}")
#         return {
#             'success': False,
#             'error': str(e)
#         }

# @shared_task
# def notify_customer_update(customer_id: int):
#     """
#     Notify all subscribers about customer data changes.
#     """
#     try:
#         # Get updated customer data
#         customer = Customer.objects.get(customer_id=customer_id)
#         serializer = CustomerSerializer(customer)
        
#         # Broadcast to customer room
#         broadcast_to_customer_websocket.delay(
#             customer_id=customer_id,
#             event_type='customer_update',
#             data={'customer': serializer.data}
#         )
        
#         # Also broadcast to general updates room
#         channel_layer = get_channel_layer()
#         async_to_sync(channel_layer.group_send)(
#             'customer_updates',
#             {
#                 'type': 'customer_update',
#                 'customer': serializer.data
#             }
#         )
        
#         return {'success': True}
        
#     except Customer.DoesNotExist:
#         logger.error(f"Customer {customer_id} not found")
#         return {'success': False, 'error': 'Customer not found'}
#     except Exception as e:
#         logger.error(f"Error notifying customer update: {str(e)}")
#         return {'success': False, 'error': str(e)}

# @shared_task
# def notify_new_platform_message(
#     message_id: int,
#     platform_identity_id: int,
#     customer_id: int
# ):
#     """
#     Notify about a new message on a platform.
#     """
#     try:
#         from ticket.models import Message
#         from ticket.serializers import MessageSerializer
        
#         # Get message data
#         message = Message.objects.get(id=message_id)
#         serializer = MessageSerializer(message)
        
#         # Broadcast to platform room
#         broadcast_to_platform_websocket.delay(
#             platform_identity_id=platform_identity_id,
#             event_type='platform_message',
#             data={'message': serializer.data}
#         )
        
#         # Also notify customer room about new message
#         broadcast_to_customer_websocket.delay(
#             customer_id=customer_id,
#             event_type='new_message_notification',
#             data={
#                 'customer_id': customer_id,
#                 'platform': message.platform_identity.platform if message.platform_identity else 'Unknown',
#                 'platform_identity_id': platform_identity_id,
#                 'message_preview': message.message[:100] if message.message else '',
#                 'timestamp': message.created_on.isoformat()
#             }
#         )
        
#         return {'success': True}
        
#     except Exception as e:
#         logger.error(f"Error notifying new platform message: {str(e)}")
#         return {'success': False, 'error': str(e)}

# @shared_task
# def notify_platform_status_change(
#     platform_identity_id: int,
#     new_status: str
# ):
#     """
#     Notify about platform connection status changes.
#     """
#     try:
#         # Get platform identity
#         platform = CustomerPlatformIdentity.objects.get(id=platform_identity_id)
        
#         # Broadcast to customer room
#         broadcast_to_customer_websocket.delay(
#             customer_id=platform.customer.customer_id,
#             event_type='platform_status_update',
#             data={
#                 'platform_identity_id': platform_identity_id,
#                 'platform': platform.platform,
#                 'status': new_status,
#                 'channel_name': platform.channel_name
#             }
#         )
        
#         return {'success': True}
        
#     except CustomerPlatformIdentity.DoesNotExist:
#         logger.error(f"Platform identity {platform_identity_id} not found")
#         return {'success': False, 'error': 'Platform identity not found'}
#     except Exception as e:
#         logger.error(f"Error notifying platform status change: {str(e)}")
#         return {'success': False, 'error': str(e)}

# @shared_task
# def broadcast_typing_indicator(
#     platform_identity_id: int,
#     is_typing: bool,
#     user_name: Optional[str] = None
# ):
#     """
#     Broadcast typing indicator for a platform conversation.
#     """
#     try:
#         broadcast_to_platform_websocket.delay(
#             platform_identity_id=platform_identity_id,
#             event_type='typing_indicator',
#             data={
#                 'is_typing': is_typing,
#                 'user_name': user_name or 'Agent'
#             }
#         )
        
#         return {'success': True}
        
#     except Exception as e:
#         logger.error(f"Error broadcasting typing indicator: {str(e)}")
#         return {'success': False, 'error': str(e)}
    


























# # Version 2.0 - Customer-centric page (v02)
"""
Updated Celery tasks for customer-centric page broadcasting
"""
from __future__ import absolute_import, unicode_literals
import json
from celery import shared_task
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
import logging
from typing import Dict, Any, Optional

from customer.models import Customer, CustomerPlatformIdentity
from ticket.models import Message
from customer.serializers import CustomerPlatformIdentitySerializer
from ticket.serializers import MessageSerializer, MessageWithFilesSerializer

logger = logging.getLogger('broadcast_debug')

channel_layer = get_channel_layer()


# # TODO - Check this and its newer version and Delete this if no longer needed
# # This version is work before new message status updates and batch processing of Chat Center page
# @shared_task
# def broadcast_platform_message_update(
#     platform_identity_id: int,
#     message_id: int
# ):
#     """
#     Broadcast new message to platform subscribers.
#     """
#     try:
#         # TODO - Log this or Delete this
#         print(f"broadcast_platform_message_update's Broadcasting message update for platform {platform_identity_id}, message {message_id}")
#         logger.info(f"Broadcasting message update for platform {platform_identity_id}, message {message_id}")

#         # Get message and platform info
#         message = Message.objects.select_related(
#             'platform_identity',
#             'platform_identity__customer'
#         ).get(id=message_id)
        
#         platform = message.platform_identity
#         if not platform:
#             logger.warning(f"Message {message_id} has no platform identity")
#             return
        
#         # Serialize message
#         message_data = MessageSerializer(message).data
        
#         # Calculate unread count for this platform
#         unread_count = Message.objects.filter(
#             platform_identity=platform,
#             is_self=False,
#             status__in=['SENT', 'DELIVERED']
#         ).count()
        
#         # # Broadcast to platform room
#         # async_to_sync(channel_layer.group_send)(
#         #     f'platform_{platform.id}',
#         #     {
#         #         'type': 'platform_message_update',
#         #         'platform_id': platform.id,
#         #         'message': message_data,
#         #         'unread_count': unread_count
#         #     }
#         # )
        
#         # # Also broadcast to customer room for UI updates
#         # async_to_sync(channel_layer.group_send)(
#         #     f'customer_{platform.customer.customer_id}',
#         #     {
#         #         'type': 'platform_message_update',
#         #         'platform_id': platform.id,
#         #         'message': message_data,
#         #         'unread_count': unread_count
#         #     }
#         # )
        
#         # Broadcast to global room for all platforms
#         async_to_sync(channel_layer.group_send)(
#             'global_platforms',  # Send to global room
#             {
#                 'type': 'platform_message_update',
#                 'platform_id': platform.id,
#                 'message': message_data,
#                 'unread_count': unread_count
#             }
#         )






#         # async_to_sync(channel_layer.group_send)(
#         #     f'platform_{platform.id}',
#         #     {
#         #         'type': 'platform_message_update',
#         #         'platform_id': platform.id,
#         #         'message': message_data,
#         #         'unread_count': unread_count
#         #     }
#         # )
        
#         # # For updating the platform list UI (latest message, unread count)
#         # # Send a separate lightweight update
#         # async_to_sync(channel_layer.group_send)(
#         #     f'customer_{platform.customer.customer_id}',
#         #     {
#         #         'type': 'platform_list_update',  # Different event type
#         #         'platform_id': platform.id,
#         #         'latest_message': {
#         #             'id': message.id,
#         #             'message': message.message,
#         #             'created_on': message.created_on.isoformat(),
#         #             'is_self': message.is_self
#         #         },
#         #         'unread_count': unread_count
#         #     }
#         # )
                
#         logger.info(f"Broadcasted message update for platform {platform.id}")
#         # TODO - Log this or Delete this
#         print(f"broadcast_platform_message_update'sroadcasted message update for platform {platform.id}")

#     except Message.DoesNotExist:
#         logger.error(f"Error broadcasting platform message: Message {message_id} not found")
#         # TODO - Log this or Delete this
#         print(f"broadcast_platform_message_update's Error broadcasting platform message: Message {message_id} not found")
#     except Exception as e:
#         logger.error(f"Error broadcasting platform message update: {str(e)}")
#         # TODO - Log this or Delete this
#         print(f"broadcast_platform_message_update's Error broadcasting platform message update: {str(e)}")


# @shared_task
# def broadcast_platform_status_change(
#     platform_identity_id: int,
#     status: str
# ):
#     """
#     Broadcast platform connection status change.
#     """
#     try:
#         platform = CustomerPlatformIdentity.objects.get(id=platform_identity_id)
        
#         # Broadcast to platform room
#         async_to_sync(channel_layer.group_send)(
#             f'platform_{platform.id}',
#             {
#                 'type': 'platform_status_update',
#                 'platform_id': platform.id,
#                 'status': status
#             }
#         )
        
#         # Broadcast to customer room
#         async_to_sync(channel_layer.group_send)(
#             f'customer_{platform.customer.customer_id}',
#             {
#                 'type': 'platform_status_update',
#                 'platform_id': platform.id,
#                 'status': status
#             }
#         )
        
#         logger.info(f"Broadcasted status change for platform {platform.id}: {status}")
        
#     except CustomerPlatformIdentity.DoesNotExist:
#         logger.error(f"Platform identity {platform_identity_id} not found")
#     except Exception as e:
#         logger.error(f"Error broadcasting platform status change: {str(e)}")


@shared_task
def update_platform_unread_count(platform_identity_id: int):
    """
    Update and broadcast unread count for a platform.
    """
    try:
        platform = CustomerPlatformIdentity.objects.get(id=platform_identity_id)
        
        # Calculate unread count
        unread_count = Message.objects.filter(
            platform_identity=platform,
            is_self=False,
            status__in=['SENT', 'DELIVERED']
        ).count()
        
        # Broadcast to customer room
        async_to_sync(channel_layer.group_send)(
            f'customer_{platform.customer.customer_id}',
            {
                'type': 'unread_count_update',
                'platform_id': platform.id,
                'count': unread_count
            }
        )
        
        logger.info(f"Updated unread count for platform {platform.id}: {unread_count}")
        
    except CustomerPlatformIdentity.DoesNotExist:
        logger.error(f"Platform identity {platform_identity_id} not found")
    except Exception as e:
        logger.error(f"Error updating unread count: {str(e)}")


@shared_task
def broadcast_typing_indicator(
    platform_identity_id: int,
    user_name: str,
    user_id: int,
    is_typing: bool
):
    """
    Broadcast typing indicator for a platform conversation.
    """
    try:
        # Broadcast to platform room
        async_to_sync(channel_layer.group_send)(
            f'platform_{platform_identity_id}',
            {
                'type': 'typing_indicator',
                'user_name': user_name,
                'user_id': user_id,
                'is_typing': is_typing
            }
        )
        
    except Exception as e:
        logger.error(f"Error broadcasting typing indicator: {str(e)}")


@shared_task
def broadcast_message_status_update(
    message_id: int,
    new_status: str
):
    """
    Broadcast message status update (sent, delivered, read).
    """
    try:
        message = Message.objects.select_related('platform_identity').get(id=message_id)
        
        if message.platform_identity:
            # Broadcast to platform room
            async_to_sync(channel_layer.group_send)(
                f'platform_{message.platform_identity.id}',
                {
                    'type': 'message_status_update',
                    'message_id': message_id,
                    'status': new_status
                }
            )
            
            logger.info(f"Broadcasted status update for message {message_id}: {new_status}")
        
    except Message.DoesNotExist:
        logger.error(f"Message {message_id} not found")
    except Exception as e:
        logger.error(f"Error broadcasting message status update: {str(e)}")


# Integration with existing ticket tasks
@shared_task
def process_new_platform_message(
    customer_id: int,
    platform_identity_id: int,
    message_content: str,
    message_type: str = 'TEXT'
):
    """
    Process new incoming message from a platform.
    This integrates with your existing ticket system.
    """
    try:
        from ticket.models import Ticket, Status
        from user.models import User
        
        platform = CustomerPlatformIdentity.objects.get(id=platform_identity_id)
        customer = platform.customer
        
        # Get or create active ticket
        active_ticket = Ticket.objects.filter(
            customer_id=customer
        ).exclude(
            status_id__name='closed'
        ).order_by('-created_on').first()
        
        if not active_ticket:
            open_status = Status.objects.get(name='open')
            system_user = User.objects.get(name='System')
            
            active_ticket = Ticket.objects.create(
                customer_id=customer,
                status_id=open_status,
                owner_id=system_user,
                created_by=system_user
            )
        
        # Create message
        message = Message.objects.create(
            ticket_id=active_ticket,
            message=message_content,
            user_name=platform.display_name or f"Customer {customer.customer_id}",
            is_self=False,  # Message from customer
            message_type=message_type,
            status=Message.MessageStatus.DELIVERED,
            platform_identity=platform
        )
        
        # Broadcast updates
        broadcast_platform_message_update.delay(platform_identity_id, message.id)
        
        # Update unread count
        update_platform_unread_count.delay(platform_identity_id)
        
        # TODO - Check and update these codes section if necessary
        # Trigger any other necessary tasks (analysis, routing, etc.)
        # ...
        
        logger.info(f"Processed new message from platform {platform_identity_id}")
        
    except Exception as e:
        logger.error(f"Error processing platform message: {str(e)}")

























# # Version 03 - broadcast_platform_message_update, _get_update_type, _check_batch_completion, retry_failed_message
# @shared_task
# # def broadcast_platform_message_update(platform_id: int, message_id: int):
# def broadcast_platform_message_update(platform_identity_id: int, message_id: int):
#     """
#     Broadcast message updates to WebSocket channels.
#     Enhanced to handle new message statuses and batch updates.
#     """
#     try:
#         # Get message with related data
#         message = Message.objects.select_related(
#             'ticket_id',
#             'platform_identity',
#             'created_by',
#             'message_template'
#         ).get(id=message_id)

#         platform = message.platform_identity
#         if not platform:
#             logger.warning(f"Message {message_id} has no platform identity")
#             return
        
#         # Serialize message
#         serializer = MessageWithFilesSerializer(message)
#         message_data = serializer.data
        
#         # Prepare broadcast payload
#         payload = {
#             'type': 'message_update',
#             'message': message_data,
#             'platform_id': platform.id,
#             # 'platform_id': platform_identity_id,
#             'batch_id': str(message.batch_id) if message.batch_id else None,
#             'status': message.status,
#             'update_type': _get_update_type(message.status)
#         }
        
#         # Get channel layer
#         channel_layer = get_channel_layer()
        
#         # # Broadcast to platform-specific channel
#         # platform_group = f'platform_{platform_id}'
#         # async_to_sync(channel_layer.group_send)(
#         #     platform_group,
#         #     {
#         #         'type': 'message.update',
#         #         'payload': payload
#         #     }
#         # )
        
#         # # Also broadcast to ticket channel
#         # ticket_group = f'ticket_{message.ticket_id.id}'
#         # async_to_sync(channel_layer.group_send)(
#         #     ticket_group,
#         #     {
#         #         'type': 'message.update',
#         #         'payload': payload
#         #     }
#         # )

#         # Calculate unread count for this platform
#         unread_count = Message.objects.filter(
#             platform_identity=platform,
#             is_self=False,
#             status__in=['SENT', 'DELIVERED']
#         ).count()

#         # Broadcast to global room for all platforms
#         async_to_sync(channel_layer.group_send)(
#             'global_platforms',  # Send to global room
#             {
#                 'type': 'platform_message_update',
#                 'platform_id': platform.id,
#                 'message': message_data,
#                 'unread_count': unread_count
#             }
#         )
        
#         # If this is part of a batch, check if batch is complete
#         if message.batch_id:
#             _check_batch_completion(message.batch_id, platform.id)
#             # _check_batch_completion(message.batch_id, platform_identity_id)
        
#         logger.info(f"Broadcasted update for message {message_id} with status {message.status}")
        
#     except Message.DoesNotExist:
#         logger.error(f"Message {message_id} not found for broadcast")
#     except Exception as e:
#         logger.error(f"Error broadcasting message update: {str(e)}")


# def _get_update_type(status: str) -> str:
#     """Determine the update type based on message status."""
#     update_types = {
#         'CREATED': 'created',
#         'UPLOADING': 'uploading',
#         'SENDING': 'sending',
#         'SENT': 'sent',
#         'DELIVERED': 'delivered',
#         'FAILED': 'failed',
#         'READ': 'read'
#     }
#     return update_types.get(status, 'update')


# def _check_batch_completion(batch_id, platform_id):
#     """
#     Check if all messages in a batch are complete and broadcast batch status.
#     """
#     try:
#         # Get all messages in batch
#         batch_messages = Message.objects.filter(batch_id=batch_id)
        
#         # Check if all are in final state
#         pending_statuses = ['CREATED', 'UPLOADING', 'SENDING']
#         pending_count = batch_messages.filter(status__in=pending_statuses).count()
        
#         if pending_count == 0:
#             # Batch is complete
#             total_count = batch_messages.count()
#             success_count = batch_messages.filter(
#                 status__in=['SENT', 'DELIVERED', 'READ']
#             ).count()
#             failed_count = batch_messages.filter(status='FAILED').count()
            
#             # Broadcast batch completion
#             channel_layer = get_channel_layer()
#             platform_group = f'platform_{platform_id}'
            
#             async_to_sync(channel_layer.group_send)(
#                 platform_group,
#                 {
#                     'type': 'batch.complete',
#                     'payload': {
#                         'batch_id': str(batch_id),
#                         'total': total_count,
#                         'successful': success_count,
#                         'failed': failed_count,
#                         'status': 'complete'
#                     }
#                 }
#             )
            
#             logger.info(f"Batch {batch_id} completed: {success_count}/{total_count} successful")
            
#     except Exception as e:
#         logger.error(f"Error checking batch completion: {str(e)}")


# @shared_task
# def retry_failed_message(message_id: int):
#     """
#     Retry a failed message (for future implementation).
#     """
#     try:
#         message = Message.objects.get(id=message_id, status='FAILED')
        
#         # Reset status
#         message.status = Message.MessageStatus.SENDING
#         message.error_detail = ''
#         message.save()
        
#         # Re-route through platform
#         # from message.services.message_creation_service import MessageCreationService
#         from ticket.services.message_creation_service import MessageCreationService
#         service = MessageCreationService()
#         service._route_and_update_message(message, message.platform_identity)
        
#     except Message.DoesNotExist:
#         logger.error(f"Message {message_id} not found or not in FAILED status")
#     except Exception as e:
#         logger.error(f"Error retrying message {message_id}: {str(e)}")




# Version 04 - 
# customer/tasks.py - Updated broadcast_platform_message_update

@shared_task
def broadcast_message_batch(platform_identity_id: int, message_ids: List[int]):
    """
    Broadcast multiple messages as a batch to WebSocket channels.
    This prevents race conditions by sending all messages in a single broadcast.

    Args:
        platform_identity_id: Platform identity ID
        message_ids: List of message IDs to broadcast
    """
    try:
        logger.info(f"=== BATCH BROADCAST START ===")
        logger.info(f"Broadcasting batch of {len(message_ids)} messages for platform_identity_id: {platform_identity_id}")
        logger.info(f"Message IDs: {message_ids}")

        # Get platform identity
        platform = CustomerPlatformIdentity.objects.get(id=platform_identity_id)

        # Get all messages with related data
        messages = Message.objects.select_related(
            'ticket_id',
            'platform_identity',
            'created_by',
            'message_template'
        ).filter(id__in=message_ids).order_by('sequence_number')

        if not messages.exists():
            logger.warning(f"No messages found for IDs: {message_ids}")
            return

        # Serialize all messages
        from ticket.serializers import MessageWithFilesSerializer
        messages_data = []
        batch_id = None

        for message in messages:
            serializer = MessageWithFilesSerializer(message)
            message_data = serializer.data
            messages_data.append(message_data)

            # Get batch_id from first message
            if batch_id is None and message.batch_id:
                batch_id = str(message.batch_id)

        # Calculate unread count for this platform
        unread_count = Message.objects.filter(
            platform_identity=platform,
            is_self=False,
            status__in=['SENT', 'DELIVERED']
        ).count()

        # Prepare the batch broadcast payload
        batch_payload = {
            'type': 'message_batch',
            'platform_id': platform.id,
            'messages': messages_data,
            'batch_id': batch_id,
            'unread_count': unread_count,
            'message_count': len(messages_data)
        }

        # Log the batch payload
        logger.info("=== BATCH BROADCAST PAYLOAD ===")
        logger.info(f"Platform ID: {platform.id}")
        logger.info(f"Message count: {len(messages_data)}")
        logger.info(f"Batch ID: {batch_id}")

        # Get channel layer
        channel_layer = get_channel_layer()

        # Broadcast batch to global room for all platforms
        logger.info(f"Broadcasting batch to channel: global_platforms")
        async_to_sync(channel_layer.group_send)(
            'global_platforms',
            {
                'type': 'message_batch_update',
                'platform_id': platform.id,
                'messages': messages_data,
                'batch_id': batch_id,
                'unread_count': unread_count
            }
        )

        # Also broadcast to ticket channel if available
        first_message = messages.first()
        if first_message and first_message.ticket_id:
            ticket_group = f'ticket_{first_message.ticket_id.id}'
            logger.info(f"Broadcasting batch to channel: {ticket_group}")
            async_to_sync(channel_layer.group_send)(
                ticket_group,
                batch_payload
            )

        logger.info(f"=== BATCH BROADCAST COMPLETE ===")
        logger.info(f"Successfully broadcasted batch of {len(messages_data)} messages for platform {platform.id}")

    except CustomerPlatformIdentity.DoesNotExist:
        logger.error(f"Platform identity {platform_identity_id} not found for batch broadcast")
    except Exception as e:
        logger.error(f"Error broadcasting message batch: {str(e)}", exc_info=True)


@shared_task
def broadcast_platform_message_update(platform_identity_id: int, message_id: int):
    """
    Broadcast message updates to WebSocket channels.
    Enhanced to handle new message statuses and ensure proper serialization.
    """
    try:
        # Log the incoming parameters
        logger.warning("broadcast_platform_message_update is executed")
        logger.info(f"=== BROADCAST START ===")
        logger.info(f"Broadcasting message_id: {message_id} for platform_identity_id: {platform_identity_id}")

        # Get message with ALL related data - CRITICAL for proper serialization
        # message = Message.objects.select_related(
        #     'ticket_id',
        #     'platform_identity',
        #     'created_by',
        #     'message_template'  # Ensure template is loaded
        # ).prefetch_related(
        #     'message_intents',  # If you have this
        #     'sub_message_intents'  # If you have this
        # ).get(id=message_id)

        message = Message.objects.select_related(
            'ticket_id',
            'platform_identity',
            'created_by',
            'message_template'
        ).get(id=message_id)
        
        platform = message.platform_identity
        if not platform:
            logger.warning(f"Message {message_id} has no platform identity")
            return
        
        # Log raw message data
        logger.info(f"Message Details:")
        logger.info(f"  - ID: {message.id}")
        logger.info(f"  - Type: {message.message_type}")
        logger.info(f"  - Status: {message.status}")
        logger.info(f"  - Is Self: {message.is_self}")
        logger.info(f"  - Content: {message.message[:100]}..." if message.message else "  - Content: [Empty]")
        logger.info(f"  - Has Template: {bool(message.message_template)}")
        if message.message_template:
            logger.info(f"  - Template ID: {message.message_template.id}")
            logger.info(f"  - Template Label: {message.message_template.label}")
        
        # CRITICAL: Use the enhanced serializer with proper context
        from ticket.serializers import MessageWithFilesSerializer
        serializer = MessageWithFilesSerializer(message)
        message_data = serializer.data
        
        # # Log the serialized data for debugging
        # logger.debug(f"Serialized message data: {json.dumps(message_data, indent=2, default=str)}")

        # Log the complete serialized data
        logger.info("=== SERIALIZED MESSAGE DATA ===")
        logger.info(json.dumps(message_data, indent=2, ensure_ascii=False, default=str))
        
        # Ensure message_template_data is included if template exists
        if message.message_template and 'message_template_data' in message_data:
            # logger.info(f"Broadcasting message {message_id} with template {message.message_template.id}")

            logger.info("=== MESSAGE TEMPLATE DATA ===")
            logger.info(json.dumps(message_data['message_template_data'], indent=2, ensure_ascii=False, default=str))
        
        
        # Calculate unread count for this platform
        unread_count = Message.objects.filter(
            platform_identity=platform,
            is_self=False,
            status__in=['SENT', 'DELIVERED']
        ).count()
        
        # Prepare the broadcast payload
        broadcast_payload = {
            'type': 'platform_message_update',
            'platform_id': platform.id,
            'message': message_data,  # This now includes properly serialized template
            'unread_count': unread_count,
            'update_type': _get_update_type(message.status),
            'batch_id': str(message.batch_id) if message.batch_id else None,
        }

        # Log the complete broadcast payload
        logger.info("=== COMPLETE BROADCAST PAYLOAD ===")
        logger.info(json.dumps(broadcast_payload, indent=2, ensure_ascii=False, default=str))
        
        # Get channel layer
        channel_layer = get_channel_layer()
        
        # Broadcast to global room for all platforms
        logger.info(f"Broadcasting to channel: global_platforms")
        async_to_sync(channel_layer.group_send)(
            'global_platforms',
            {
                'type': 'platform_message_update',
                'platform_id': platform.id,
                'message': message_data,
                'unread_count': unread_count
            }
        )
        
        # # Also broadcast to platform-specific channel if you have it
        # platform_group = f'platform_{platform.id}'
        # logger.info(f"Broadcasting to channel: {platform_group}")
        # async_to_sync(channel_layer.group_send)(
        #     platform_group,
        #     broadcast_payload
        # )
        
        # Broadcast to ticket channel as well
        if message.ticket_id:
            ticket_group = f'ticket_{message.ticket_id.id}'
            logger.info(f"Broadcasting to channel: {ticket_group}")
            async_to_sync(channel_layer.group_send)(
                ticket_group,
                broadcast_payload
            )
        
        # logger.info(f"Broadcasted update for message {message_id} with status {message.status}")

        logger.info(f"=== BROADCAST COMPLETE ===")
        logger.info(f"Successfully broadcasted message {message_id} with status {message.status}")
        
        # Log summary for frontend debugging
        logger.info("=== FRONTEND DEBUGGING SUMMARY ===")
        logger.info(f"Message Type: {message.message_type}")
        logger.info(f"Has Template: {bool(message_data.get('message_template'))}")
        logger.info(f"Has Template Data: {bool(message_data.get('message_template_data'))}")
        if message_data.get('message_template_data'):
            template_data = message_data['message_template_data']
            logger.info(f"Template Elements:")
            logger.info(f"  - Has text: {bool(template_data.get('text'))}")
            logger.info(f"  - Has quick_reply: {bool(template_data.get('quick_reply'))}")
            logger.info(f"  - Has image_map: {bool(template_data.get('image_map'))}")
            logger.info(f"  - Has carousel: {bool(template_data.get('carousel'))}")
            logger.info(f"  - Has image_carousel: {bool(template_data.get('image_carousel'))}")
        
        # If this is part of a batch, check if batch is complete
        if message.batch_id:
            _check_batch_completion(message.batch_id, platform.id)
        
    except Message.DoesNotExist:
        logger.error(f"Message {message_id} not found for broadcast")
    except Exception as e:
        logger.error(f"Error broadcasting message update: {str(e)}", exc_info=True)


def _get_update_type(status: str) -> str:
    """Determine the update type based on message status."""
    update_types = {
        'CREATED': 'created',
        'UPLOADING': 'uploading',
        'SENDING': 'sending',
        'SENT': 'sent',
        'DELIVERED': 'delivered',
        'FAILED': 'failed',
        'READ': 'read'
    }
    return update_types.get(status, 'update')


# def _check_batch_completion(batch_id, platform_id):
#     """
#     Check if all messages in a batch are complete and broadcast batch status.
#     """
#     try:
#         # Get all messages in batch
#         batch_messages = Message.objects.filter(batch_id=batch_id)
        
#         # Check if all are in final state
#         pending_statuses = ['CREATED', 'UPLOADING', 'SENDING']
#         pending_count = batch_messages.filter(status__in=pending_statuses).count()
        
#         if pending_count == 0:
#             # Batch is complete
#             total_count = batch_messages.count()
#             success_count = batch_messages.filter(
#                 status__in=['SENT', 'DELIVERED', 'READ']
#             ).count()
#             failed_count = batch_messages.filter(status='FAILED').count()
            
#             # Broadcast batch completion
#             channel_layer = get_channel_layer()
#             platform_group = f'platform_{platform_id}'
            
#             async_to_sync(channel_layer.group_send)(
#                 platform_group,
#                 {
#                     'type': 'batch.complete',
#                     'payload': {
#                         'batch_id': str(batch_id),
#                         'total': total_count,
#                         'successful': success_count,
#                         'failed': failed_count,
#                         'status': 'complete'
#                     }
#                 }
#             )
            
#             # Also broadcast to global platforms
#             async_to_sync(channel_layer.group_send)(
#                 'global_platforms',
#                 {
#                     'type': 'batch_complete',
#                     'batch_id': str(batch_id),
#                     'platform_id': platform_id,
#                     'summary': {
#                         'total': total_count,
#                         'successful': success_count,
#                         'failed': failed_count
#                     }
#                 }
#             )
            
#             logger.info(f"Batch {batch_id} completed: {success_count}/{total_count} successful")
            
#     except Exception as e:
#         logger.error(f"Error checking batch completion: {str(e)}")

def _check_batch_completion(batch_id, platform_id):
    """Check if all messages in a batch are complete and broadcast batch status."""
    try:
        # Get all messages in batch
        batch_messages = Message.objects.filter(batch_id=batch_id)
        
        # Check if all are in final state
        pending_statuses = ['CREATED', 'UPLOADING', 'SENDING']
        pending_count = batch_messages.filter(status__in=pending_statuses).count()
        
        if pending_count == 0:
            # Batch is complete
            total_count = batch_messages.count()
            success_count = batch_messages.filter(
                status__in=['SENT', 'DELIVERED', 'READ']
            ).count()
            failed_count = batch_messages.filter(status='FAILED').count()
            
            batch_complete_payload = {
                'batch_id': str(batch_id),
                'platform_id': platform_id,
                'summary': {
                    'total': total_count,
                    'successful': success_count,
                    'failed': failed_count
                }
            }
            
            logger.info("=== BATCH COMPLETE ===")
            logger.info(json.dumps(batch_complete_payload, indent=2, default=str))
            
            # Broadcast batch completion
            channel_layer = get_channel_layer()
            
            # Broadcast to global platforms
            async_to_sync(channel_layer.group_send)(
                'global_platforms',
                {
                    'type': 'batch_complete',
                    **batch_complete_payload
                }
            )
            
            # Also to platform-specific channel
            platform_group = f'platform_{platform_id}'
            async_to_sync(channel_layer.group_send)(
                platform_group,
                {
                    'type': 'batch.complete',
                    'payload': batch_complete_payload
                }
            )
            
            logger.info(f"Batch {batch_id} completed: {success_count}/{total_count} successful")
            
    except Exception as e:
        logger.error(f"Error checking batch completion: {str(e)}", exc_info=True)

# @shared_task
# def retry_failed_message(message_id: int):
#     """
#     Retry a failed message (for future implementation).
#     """
#     try:
#         message = Message.objects.get(id=message_id, status='FAILED')
        
#         # Reset status
#         message.status = Message.MessageStatus.SENDING
#         message.error_detail = ''
#         message.save()
        
#         # Re-route through platform
#         # from message.services.message_creation_service import MessageCreationService
#         from ticket.services.message_creation_service import MessageCreationService
#         service = MessageCreationService()
#         service._route_and_update_message(message, message.platform_identity)
        
#     except Message.DoesNotExist:
#         logger.error(f"Message {message_id} not found or not in FAILED status")
#     except Exception as e:
#         logger.error(f"Error retrying message {message_id}: {str(e)}")