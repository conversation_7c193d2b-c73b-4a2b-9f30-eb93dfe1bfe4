import json
import logging
from typing import Set
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.db.models import Q, Count, Max
from asgiref.sync import sync_to_async

from customer.models import Customer, CustomerPlatformIdentity
from ticket.models import Message
from customer.serializers import CustomerPlatformIdentityWithCustomerSerializer
from ticket.serializers import MessageSerializer

logger = logging.getLogger('django.connector')


class GlobalPlatformConsumer(AsyncWebsocketConsumer):
    """
    WebSocket consumer for global platform updates.
    Handles all platform identities across all customers.
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.user = None
        self.subscribed_platforms: Set[int] = set()
    
    async def connect(self):
        """Handle WebSocket connection."""
        self.user = self.scope["user"]
        
        # Join global platforms room
        self.room_group_name = 'global_platforms'
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        await self.accept()
        
        # Send connection confirmation
        await self.send(text_data=json.dumps({
            'type': 'connection_established',
            'message': 'Connected to global platform updates'
        }))
        
        logger.info("Global Platform WebSocket connected")
    
    async def disconnect(self, close_code):
        """Handle WebSocket disconnection."""
        # Leave global room
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )
        
        # Leave all subscribed platform rooms
        for platform_id in self.subscribed_platforms:
            await self.channel_layer.group_discard(
                f'platform_{platform_id}',
                self.channel_name
            )
        
        logger.info("Global Platform WebSocket disconnected")
    
    async def receive(self, text_data):
        """Handle incoming WebSocket messages."""
        try:
            data = json.loads(text_data)
            action = data.get('action')
            
            handlers = {
                'subscribe_platform': self.handle_subscribe_platform,
                'unsubscribe_platform': self.handle_unsubscribe_platform,
                'subscribe_multiple': self.handle_subscribe_multiple,
                'get_platform_updates': self.handle_get_platform_updates,
                'ping': self.handle_ping
            }
            
            handler = handlers.get(action)
            if handler:
                await handler(data)
            else:
                await self.send_error(f"Unknown action: {action}")
        
        except json.JSONDecodeError:
            await self.send_error("Invalid JSON format")
        except Exception as e:
            logger.error(f"Error in global platform consumer: {str(e)}")
            await self.send_error(str(e))
    
    async def handle_subscribe_platform(self, data):
        """Subscribe to a specific platform's updates."""
        platform_id = data.get('platform_id')
        
        if not platform_id:
            await self.send_error("Platform ID is required")
            return
        
        if platform_id not in self.subscribed_platforms:
            self.subscribed_platforms.add(platform_id)
            await self.channel_layer.group_add(
                f'platform_{platform_id}',
                self.channel_name
            )
            
            await self.send(text_data=json.dumps({
                'type': 'subscribed',
                'platform_id': platform_id
            }))
            
            logger.info(f"Subscribed to platform {platform_id}")
    
    async def handle_unsubscribe_platform(self, data):
        """Unsubscribe from a platform's updates."""
        platform_id = data.get('platform_id')
        
        if platform_id and platform_id in self.subscribed_platforms:
            self.subscribed_platforms.remove(platform_id)
            await self.channel_layer.group_discard(
                f'platform_{platform_id}',
                self.channel_name
            )
            
            await self.send(text_data=json.dumps({
                'type': 'unsubscribed',
                'platform_id': platform_id
            }))
    
    async def handle_subscribe_multiple(self, data):
        """Subscribe to multiple platforms at once."""
        platform_ids = data.get('platform_ids', [])
        
        subscribed = []
        for platform_id in platform_ids:
            if platform_id not in self.subscribed_platforms:
                self.subscribed_platforms.add(platform_id)
                await self.channel_layer.group_add(
                    f'platform_{platform_id}',
                    self.channel_name
                )
                subscribed.append(platform_id)
        
        await self.send(text_data=json.dumps({
            'type': 'subscribed_multiple',
            'platform_ids': subscribed
        }))
    
    async def handle_get_platform_updates(self, data):
        """Get latest updates for specified platforms."""
        platform_ids = data.get('platform_ids', [])
        
        if not platform_ids:
            platform_ids = list(self.subscribed_platforms)
        
        updates = await self._get_platform_updates(platform_ids)
        
        await self.send(text_data=json.dumps({
            'type': 'platform_updates',
            'updates': updates
        }))
    
    async def handle_ping(self, data):
        """Handle ping to keep connection alive."""
        await self.send(text_data=json.dumps({
            'type': 'pong',
            'timestamp': data.get('timestamp')
        }))
    
    # Channel layer event handlers
    async def platform_message_update(self, event):
        """Handle new message for a platform."""
        await self.send(text_data=json.dumps({
            'type': 'new_message',
            'platform_id': event['platform_id'],
            'message': event['message'],
            'unread_count': event.get('unread_count', 0)
        }))
    
    async def platform_status_update(self, event):
        """Handle platform status change."""
        await self.send(text_data=json.dumps({
            'type': 'platform_status',
            'platform_id': event['platform_id'],
            'status': event['status'],
            'customer_id': event.get('customer_id')
        }))
    
    async def platform_typing_indicator(self, event):
        """Handle typing indicator."""
        await self.send(text_data=json.dumps({
            'type': 'typing_indicator',
            'platform_id': event['platform_id'],
            'is_typing': event['is_typing'],
            'user_name': event.get('user_name', 'Customer')
        }))

    async def message_batch_update(self, event):
        """Handle batch message updates."""
        # Send each message in the batch as individual new_message events
        # This maintains compatibility with existing frontend handlers
        messages = event.get('messages', [])
        platform_id = event.get('platform_id')
        unread_count = event.get('unread_count', 0)

        logger.info(f"Processing batch of {len(messages)} messages for platform {platform_id}")

        # Send each message individually to maintain existing frontend compatibility
        for message in messages:
            await self.send(text_data=json.dumps({
                'type': 'new_message',
                'platform_id': platform_id,
                'message': message,
                'unread_count': unread_count
            }))

        # Also send a batch completion event
        await self.send(text_data=json.dumps({
            'type': 'message_batch_complete',
            'platform_id': platform_id,
            'batch_id': event.get('batch_id'),
            'message_count': len(messages)
        }))

    async def batch_complete(self, event):
        """Handle batch completion events."""
        # Extract data from event
        batch_data = {
            'type': 'batch_complete',
            'batch_id': event.get('batch_id'),
            'platform_id': event.get('platform_id'),
            'summary': event.get('summary', {})
        }

        # Log the batch completion
        logger.info(f"Batch complete event: {batch_data}")

        # Send to frontend
        await self.send(text_data=json.dumps(batch_data))
    
    async def send_error(self, message: str):
        """Send error message."""
        await self.send(text_data=json.dumps({
            'type': 'error',
            'message': message
        }))
    
    # Database methods
    @database_sync_to_async
    def _get_platform_updates(self, platform_ids: list):
        """Get latest updates for platforms."""
        updates = {}
        
        for platform_id in platform_ids:
            try:
                platform = CustomerPlatformIdentity.objects.select_related('customer').get(
                    id=platform_id,
                    is_active=True
                )
                
                # Get latest message
                latest_message = Message.objects.filter(
                    platform_identity=platform
                ).order_by('-created_on').first()
                
                # Get unread count
                unread_count = Message.objects.filter(
                    platform_identity=platform,
                    is_self=False,
                    status__in=['SENT', 'DELIVERED']
                ).count()
                
                updates[platform_id] = {
                    'platform': CustomerPlatformIdentityWithCustomerSerializer(platform).data,
                    'latest_message': MessageSerializer(latest_message).data if latest_message else None,
                    'unread_count': unread_count
                }
                
            except CustomerPlatformIdentity.DoesNotExist:
                continue
        
        return updates