import uuid
import logging
from typing import List, Dict, Any, Tuple, Optional
from django.db import transaction
from django.utils import timezone

from setting.models import MessageTemplate
from ticket.models import Message
from customer._services.message_file_service import MessageFileService
# from ticket.services.message_file_service import MessageFileService
from customer.tasks import broadcast_platform_message_update, broadcast_message_batch
from connectors.services.platform_routing_service import PlatformRoutingService

# logger = logging.getLogger(__name__)
logger = logging.getLogger('django.ticket')

class MessageCreationService:
    """Service for creating and managing multiple messages from a single user action."""
    
    def __init__(self):
        self.file_service = MessageFileService()
    
    def create_message_batch(
        self,
        platform_identity,
        ticket,
        user,
        message_content: str = "",
        files: List = None,
        message_template_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Create a batch of messages from user input.
        
        Args:
            platform_identity: CustomerPlatformIdentity instance
            ticket: Active ticket for the conversation
            user: User creating the messages
            message_content: Text content (optional)
            files: List of file objects (optional)
            message_template_id: ID of message template to use (optional)
        
        Returns:
            Dict with batch results including created messages and summary
        """
        batch_id = uuid.uuid4()
        created_messages = []
        failed_items = []
        sequence = 0
        
        # Start atomic transaction for message creation
        with transaction.atomic():
            # Create text message if content exists
            if message_content.strip():
                try:
                    text_message = self.create_text_message(
                        platform_identity=platform_identity,
                        ticket=ticket,
                        user=user,
                        message_content=message_content,
                        batch_id=batch_id,
                        sequence_number=sequence,
                        message_template_id=message_template_id
                    )
                    created_messages.append(text_message)
                    sequence += 1
                    
                    # Route text message immediately
                    self._route_and_update_message(text_message, platform_identity)
                    
                except Exception as e:
                    logger.error(f"Failed to create text message: {str(e)}")
                    failed_items.append({
                        'type': 'text',
                        'content': message_content[:50] + '...' if len(message_content) > 50 else message_content,
                        'error': str(e)
                    })
            
            # Process files individually
            if files:
                for file in files:
                    try:
                        file_message = self.create_file_message(
                            platform_identity=platform_identity,
                            ticket=ticket,
                            user=user,
                            file=file,
                            batch_id=batch_id,
                            sequence_number=sequence
                        )
                        created_messages.append(file_message)
                        sequence += 1
                        
                    except Exception as e:
                        logger.error(f"Failed to process file {file.name}: {str(e)}")
                        failed_items.append({
                            'type': 'file',
                            'content': file.name,
                            'error': str(e)
                        })
        
        # Return batch results
        return {
            'batch_id': str(batch_id),
            'messages': created_messages,
            'failed_items': failed_items,
            'summary': {
                'total_attempted': len(created_messages) + len(failed_items),
                'successful': len(created_messages),
                'failed': len(failed_items)
            }
        }

    def create_message_batch_with_preloaded_files(
        self,
        platform_identity,
        ticket,
        user,
        message_content: str = "",
        pre_uploaded_files: List[Dict] = None,
        message_template_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Create a batch of messages from user input using pre-uploaded files.
        This method is used when files have been uploaded separately via the upload endpoint.

        Args:
            platform_identity: CustomerPlatformIdentity instance
            ticket: Active ticket for the conversation
            user: User creating the messages
            message_content: Text content (optional)
            pre_uploaded_files: List of pre-uploaded file data with URLs and metadata
            message_template_id: ID of message template to use (optional)

        Returns:
            Dict with batch results including created messages and summary
        """
        batch_id = uuid.uuid4()
        created_messages = []
        failed_items = []
        sequence = 0

        # Start atomic transaction for message creation
        with transaction.atomic():
            # Create text message if content exists
            if message_content and message_content.strip():
                try:
                    text_message = self.create_text_message_without_broadcast(
                        platform_identity=platform_identity,
                        ticket=ticket,
                        user=user,
                        message_content=message_content,
                        batch_id=batch_id,
                        sequence_number=sequence,
                        message_template_id=message_template_id
                    )
                    created_messages.append(text_message)
                    sequence += 1

                except Exception as e:
                    logger.error(f"Failed to create text message: {str(e)}")
                    failed_items.append({
                        'type': 'text',
                        'content': message_content,
                        'error': str(e)
                    })

            # Process pre-uploaded files
            if pre_uploaded_files:
                for file_data in pre_uploaded_files:
                    try:
                        file_message = self.create_file_message_from_preloaded_without_broadcast(
                            platform_identity=platform_identity,
                            ticket=ticket,
                            user=user,
                            file_data=file_data,
                            batch_id=batch_id,
                            sequence_number=sequence
                        )
                        created_messages.append(file_message)
                        sequence += 1

                    except Exception as e:
                        logger.error(f"Failed to create message for pre-uploaded file: {str(e)}")
                        failed_items.append({
                            'type': 'file',
                            'content': file_data.get('metadata', {}).get('name', 'Unknown file'),
                            'error': str(e)
                        })

        # After transaction commits, broadcast all messages as a batch and route them
        if created_messages:
            # Schedule batch broadcast after transaction commits
            message_ids = [msg.id for msg in created_messages]
            transaction.on_commit(
                lambda: broadcast_message_batch.delay(platform_identity.id, message_ids)
            )

            # Route messages after broadcast is scheduled
            for message in created_messages:
                self._route_and_update_message(message, platform_identity)

        # Return batch results
        return {
            'batch_id': str(batch_id),
            'messages': created_messages,
            'failed_items': failed_items,
            'summary': {
                'total_attempted': len(created_messages) + len(failed_items),
                'successful': len(created_messages),
                'failed': len(failed_items)
            }
        }
    
    def create_text_message(
        self,
        platform_identity,
        ticket,
        user,
        message_content: str,
        batch_id: uuid.UUID,
        sequence_number: int,
        message_template_id: Optional[int] = None
    ) -> Message:
        """Create a text message."""
        
        # Get message template if provided
        message_template = None
        if message_template_id:
            # from message.models import MessageTemplate
            try:
                message_template = MessageTemplate.objects.get(
                    id=message_template_id,
                    is_active=True
                )
            except MessageTemplate.DoesNotExist:
                logger.warning(f"Message template {message_template_id} not found")
        
        # Create message
        message = Message.objects.create(
            ticket_id=ticket,
            message=message_content,
            user_name=user.get_full_name() or user.username,
            is_self=True,
            message_type=Message.MessageType.TEXT,
            status=Message.MessageStatus.SENDING,
            platform_identity=platform_identity,
            created_by=user,
            batch_id=batch_id,
            sequence_number=sequence_number,
            message_template=message_template
        )
        
        # Broadcast creation
        broadcast_platform_message_update.delay(platform_identity.id, message.id)
        
        return message

    def create_text_message_without_broadcast(
        self,
        platform_identity,
        ticket,
        user,
        message_content: str,
        batch_id: uuid.UUID,
        sequence_number: int,
        message_template_id: Optional[int] = None
    ) -> Message:
        """
        Create a text message without broadcasting (for batch processing).
        Same as create_text_message but without the immediate broadcast.
        """
        # Get message template if provided
        message_template = None
        if message_template_id:
            try:
                message_template = MessageTemplate.objects.get(id=message_template_id)
            except MessageTemplate.DoesNotExist:
                logger.warning(f"Message template {message_template_id} not found")

        # Create message
        message = Message.objects.create(
            ticket_id=ticket,
            message=message_content,
            user_name=user.get_full_name() or user.username,
            is_self=True,
            message_type=Message.MessageType.TEXT,
            status=Message.MessageStatus.SENDING,
            platform_identity=platform_identity,
            created_by=user,
            batch_id=batch_id,
            sequence_number=sequence_number,
            message_template=message_template
        )

        # No broadcast here - will be handled by batch broadcast
        return message

    def create_file_message(
        self,
        platform_identity,
        ticket,
        user,
        file,
        batch_id: uuid.UUID,
        sequence_number: int
    ) -> Message:
        """Create a file message with upload."""
        
        # Validate file first
        is_valid, error_msg = self.file_service.validate_file(file)
        if not is_valid:
            raise ValueError(error_msg)
        
        # Determine file type
        file_type = Message.MessageType.IMAGE if file.content_type.startswith('image/') else Message.MessageType.FILE
        
        # Create placeholder message
        message = Message.objects.create(
            ticket_id=ticket,
            message=f"Uploading {file.name}...",
            user_name=user.get_full_name() or user.username,
            is_self=True,
            message_type=file_type,
            status=Message.MessageStatus.UPLOADING,
            platform_identity=platform_identity,
            created_by=user,
            batch_id=batch_id,
            sequence_number=sequence_number,
            has_attachments=True
        )
        
        # TODO - Check is this one cause some invalid message on Chat Center page
        # Broadcast uploading status
        broadcast_platform_message_update.delay(platform_identity.id, message.id)
        
        try:
            # Upload file
            blob_path = user.get_message_file_path(
                file.name,
                platform_identity.customer.customer_id,
                ticket.id
            )
            url, metadata = self.file_service.upload_file(file, blob_path)
            
            # Update message with file info
            message.file_url = [url]
            message.file_metadata = {'files': [metadata]}
            message.message = file.name  # Update with actual filename
            message.status = Message.MessageStatus.SENDING
            message.save()
            
            # Route through platform
            self._route_and_update_message(message, platform_identity)
            
        except Exception as e:
            # Mark as failed
            message.mark_as_failed(f"Upload failed: {str(e)}")
            broadcast_platform_message_update.delay(platform_identity.id, message.id)
            raise
        
        return message

    def create_file_message_from_preloaded(
        self,
        platform_identity,
        ticket,
        user,
        file_data: Dict,
        batch_id: uuid.UUID,
        sequence_number: int
    ) -> Message:
        """
        Create a file message from pre-uploaded file data.
        This method is used when files have been uploaded separately via the upload endpoint.

        Args:
            platform_identity: CustomerPlatformIdentity instance
            ticket: Active ticket
            user: User creating the message
            file_data: Pre-uploaded file data containing URL and metadata
            batch_id: Batch identifier for grouping messages
            sequence_number: Sequence within the batch

        Returns:
            Created Message instance
        """
        metadata = file_data['metadata']

        # Determine file type based on content type
        file_type = Message.MessageType.IMAGE if metadata['type'].startswith('image/') else Message.MessageType.FILE

        # Create message with SENDING status (skip UPLOADING since file is already uploaded)
        message = Message.objects.create(
            ticket_id=ticket,
            message=metadata['name'],  # Use filename as message content
            user_name=user.get_full_name() or user.username,
            is_self=True,
            message_type=file_type,
            status=Message.MessageStatus.SENDING,  # Direct to SENDING status
            platform_identity=platform_identity,
            created_by=user,
            batch_id=batch_id,
            sequence_number=sequence_number,
            has_attachments=True,
            file_url=[file_data['url']],  # Store the pre-uploaded file URL
            file_metadata={'files': [metadata]}  # Store the file metadata
        )

        # Broadcast initial message creation for real-time UI updates
        broadcast_platform_message_update.delay(platform_identity.id, message.id)

        # Route through platform for delivery
        self._route_and_update_message(message, platform_identity)

        return message

    def create_file_message_from_preloaded_without_broadcast(
        self,
        platform_identity,
        ticket,
        user,
        file_data: Dict,
        batch_id: uuid.UUID,
        sequence_number: int
    ) -> Message:
        """
        Create a file message from pre-uploaded file data without broadcasting (for batch processing).
        Same as create_file_message_from_preloaded but without the immediate broadcast.
        """
        # Extract metadata
        metadata = file_data.get('metadata', {})

        # Determine file type based on content type
        content_type = metadata.get('type', 'application/octet-stream')
        file_type = Message.MessageType.IMAGE if content_type.startswith('image/') else Message.MessageType.FILE

        # Create message with SENDING status (skip UPLOADING since file is already uploaded)
        message = Message.objects.create(
            ticket_id=ticket,
            message=metadata['name'],  # Use filename as message content
            user_name=user.get_full_name() or user.username,
            is_self=True,
            message_type=file_type,
            status=Message.MessageStatus.SENDING,  # Direct to SENDING status
            platform_identity=platform_identity,
            created_by=user,
            batch_id=batch_id,
            sequence_number=sequence_number,
            has_attachments=True,
            file_url=[file_data['url']],  # Store the pre-uploaded file URL
            file_metadata={'files': [metadata]}  # Store the file metadata
        )

        # No broadcast here - will be handled by batch broadcast
        return message

    def _route_and_update_message(self, message: Message, platform_identity) -> None:
        """Route message through platform and update status."""
        try:
            # Prepare content based on message type
            if message.message_type == Message.MessageType.TEXT:
                content = message.message
            else:
                # For files, send the file URL or handle as per platform requirements
                content = message.message  # Filename
                if message.file_url:
                    # Some platforms might need the URL instead
                    content = message.file_url[0] if message.file_url else message.message
            
            # Route through platform
            result = PlatformRoutingService.route_message_to_customer(
                customer=platform_identity.customer,
                message_content=content,
                message_type=message.message_type,
                preferred_platform_identity_id=platform_identity.id,
                metadata={
                    'message_id': message.id,
                    'ticket_id': message.ticket_id.id,
                    'batch_id': str(message.batch_id) if message.batch_id else None,
                    'file_urls': message.file_url,
                    'file_metadata': message.file_metadata
                }
            )
            
            # Update status based on result
            if result.get('success'):
                message.status = Message.MessageStatus.DELIVERED
            else:
                message.status = Message.MessageStatus.FAILED
                message.error_detail = result.get('error', 'Platform routing failed')
            
            message.save()
            
        except Exception as e:
            logger.error(f"Error routing message {message.id}: {str(e)}")
            message.mark_as_failed(f"Routing failed: {str(e)}")
        
        finally:
            # Always broadcast final status
            broadcast_platform_message_update.delay(platform_identity.id, message.id)
    
    def process_file_async(self, message_id: int, file_data: bytes, file_info: Dict) -> None:
        """
        Process file upload asynchronously (for future enhancement).
        This would be called from a Celery task for large files.
        """
        # TODO: Implement async file processing for large files
        pass